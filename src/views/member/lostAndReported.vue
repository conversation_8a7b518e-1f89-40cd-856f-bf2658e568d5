<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="main-search"
             label-width="68px"
             size="small">
      <el-form-item class="custom-label" label="挂失会员号" prop="userNo">
        <el-input
          v-model="queryParams.userNo"
          clearable
          placeholder="请输入会员号"
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          clearable
          placeholder="请输入会员手机号"
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="queryParams.name"
          clearable
          placeholder="请输入会员名称"
          style="width: 150px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery(false)">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button
          v-hasPermi="['member:add']"
          icon="el-icon-download"
          size="mini" type="danger"
          @click="handleQuery(true)"
        >导出</el-button>

      </el-form-item>
    </el-form>

    <el-table ref="tables" v-loading="loading" :data="list" :default-sort="defaultSort"
              @selection-change="handleSelectionChange" @sort-change="handleSortChange">
      <el-table-column align="center" type="selection" width="100"/>
      <el-table-column align="center" label="会员名称" prop="name"/>
      <el-table-column align="center" label="挂失会员号" prop="userNo" width="150"/>
      <el-table-column align="center" label="上一次会员号" prop="lastUserNo" width="150"/>
      <el-table-column align="center" label="挂失时间" prop="updateTime">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="挂失原因" prop="reasonForReportingLoss"/>
      <el-table-column align="center" label="操作人" prop="operator"/>
      <el-table-column align="center" label="手机号" prop="mobile">
        <template slot-scope="scope">
          <span>{{ scope.row.mobile ? scope.row.mobile : '-' }}</span>
        </template>
      </el-table-column>


      <!--      <el-table-column label="操作" align="center" width="120" fixed='right'>-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button-->
      <!--            size="mini"-->
      <!--            type="text"-->
      <!--            icon="el-icon-edit"-->
      <!--            v-hasPermi="['member:edit']"-->
      <!--            @click="handleUpdate(scope.row)"-->
      <!--          >编辑</el-button>-->
      <!--          <el-button-->
      <!--            size="mini"-->
      <!--            type="text"-->
      <!--            icon="el-icon-delete"-->
      <!--            v-hasPermi="['member:delete']"-->
      <!--            @click="handleDelete(scope.row)"-->
      <!--          >删除</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>

    <!--    修改会员信息-->
    <!--    <el-dialog :title="title" :visible.sync="open" class="common-dialog" width="800px" append-to-body>-->
    <!--      <el-form ref="form" :model="form" :rules="rules" label-width="120px">-->
    <!--        <el-row>-->
    <!--          <el-col :span="24">-->
    <!--            <el-form-item label="会员名称" prop="name" style="width: 420px">-->
    <!--              <el-input v-model="form.name" placeholder="请输入会员名称" maxlength="30" />-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--        </el-row>-->
    <!--        <el-row>-->
    <!--          <el-col :span="24">-->
    <!--            <el-form-item label="挂失会员号" prop="userNo">-->
    <!--              <el-input v-model="form.userNo" placeholder="请输入会员号" maxlength="30" />-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--        </el-row>-->
    <!--        <el-row>-->
    <!--          <el-col :span="24">-->
    <!--            <el-form-item label="上一次会员号" prop="lastUserNo">-->
    <!--              <el-input v-model="form.lastUserNo" placeholder="请输入上一次会员号" maxlength="30"/>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--        </el-row>-->
    <!--        <el-row>-->
    <!--          <el-col :span="24">-->
    <!--            <el-form-item label="挂失原因" prop="reasonForReportingLoss">-->
    <!--              <el-input v-model="form.reasonForReportingLoss" placeholder="请输入挂失原因" maxlength="30"/>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--        </el-row>-->
    <!--        <el-row>-->
    <!--          <el-col :span="24">-->
    <!--            <el-form-item label="操作人" prop="operator">-->
    <!--              <el-input v-model="form.operator" placeholder="请输入挂失会员卡操作人" maxlength="30"/>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--        </el-row>-->
    <!--        <el-row>-->
    <!--          <el-col :span="24">-->
    <!--            <el-form-item label="手机号" prop="mobile">-->
    <!--              <el-input v-model="form.mobile" placeholder="请输入手机号" maxlength="30"/>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--        </el-row>-->


    <!--      </el-form>-->
    <!--      <div slot="footer" class="dialog-footer">-->
    <!--        <el-button type="primary" @click="submitForm">确定</el-button>-->
    <!--        <el-button @click="cancel">取消</el-button>-->
    <!--      </div>-->
    <!--    </el-dialog>-->

    <pagination
      v-show="total>0"
      :limit.sync="queryParams.pageSize"
      :page.sync="queryParams.page"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {deleteMember, getMemberInfo, getMemberList, saveMember, updateMemberStatus} from "@/api/member";
import balanceRecharge from "./balanceRecharge";
import pointRecharge from "./pointRecharge";
import {parseTime} from "../../utils/fuint";
import axios from 'axios';
import { getToken } from '@/utils/auth';

export default {
  name: "MemberIndex",
  components: {balanceRecharge, pointRecharge},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 标题
      title: "",
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 会员等级列表
      userGradeList: [],
      // 会员分组列表
      groupList: [],
      // 店铺列表
      storeList: [],
      storeIds: [],
      // 是否显示修改对话框
      open: false,
      // 当前操作用户
      userId: '',
      // 是否弹层充值
      openBalance: false,
      // 是否弹层积分
      openPoint: false,
      // 日期范围
      dateRange: [],
      // 默认排序
      defaultSort: {prop: 'updateTime', order: 'descending'},
      // 表单参数
      form: {
        name: '',
        userNo: '',
        lastUserNo: '',
        updateTime: '',
        reasonForReportingLoss: '',
        operator: '',
        mobile: ''
      },
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        name: '',
        userNo: '',
        operator: '',
        mobile: ''
      },
      // 表单校验
      rules: {
        name: [
          {required: true, message: "会员名称不能为空", trigger: "blur"},
          {min: 2, max: 200, message: '会员名称长度必须介于2 和 100 之间', trigger: 'blur'}
        ],
        gradeId: [{required: true, message: "请选择会员等级", trigger: "blur"}]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    parseTime,
    // 查询列表
    getList() {
      this.loading = true;
      this.queryParams.storeIds = this.storeIds ? this.storeIds.join(",") : '';
      getMemberList(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          // 只显示有上一次会员号的数据
          const filteredContent = response.data.paginationResponse.content.filter(
            item => item.lastUserNo && item.lastUserNo.trim() !== ''
          );
          this.list = filteredContent;
          // 使用过滤后的实际数据条数作为总条数
          this.total = filteredContent.length;
          this.userGradeList = response.data.userGradeList;
          this.storeList = response.data.storeList;
          this.groupList = response.data.groupList;
          this.loading = false;
        }
      );
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.storeIds = '';
      this.storeIds = [];
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order);
      this.handleQuery();
    },
    // 状态修改
    handleStatusChange(row) {
      let text = row.status == "A" ? "启用" : "禁用";
      this.$modal.confirm('确认要' + text + '"' + row.name + '"吗？').then(function () {
        return updateMemberStatus(row.id, row.status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function () {
        row.status = row.status === "N" ? "A" : "N";
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    // 排序触发事件
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    // 关闭对话框
    closeDialog(dialog) {
      console.log('closeDialog');
      if (dialog == 'balance') {
        this.openBalance = false;
      }
      if (dialog == 'point') {
        this.openPoint = false;
      }
      this.userId = "";
      this.getList();
    },

    // 表单重置
    reset() {
      this.resetForm("form");
      this.form.id = '';
      this.form.description = '';
      this.form.name = '';
      this.form.startTime = '';
      this.form.endTime = '';
      this.form.groupId = '';
      this.form.storeId = '';
      this.form.gradeId = '';
      this.form.userNo = '';
      this.form.mobile = '';
      this.form.groupInfo = {};
    },
    // 提交按钮
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (!this.form.id) {
            this.$modal.msgError("当前不允许新增挂失会员信息");
            return;
          }
          // 只有修改操作
          saveMember(this.form).then(response => {
            this.$modal.msgSuccess("修改挂失会员信息成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 修改按钮操作
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMemberInfo(id).then(response => {
        this.form = response.data.memberInfo;
        this.open = true;
        this.title = "编辑会员";
      });
    },
    // 删除按钮操作
    handleDelete(row) {
      const name = row.name;
      this.$modal.confirm('确定删除"' + name + '"的会员信息？').then(function () {
        return deleteMember(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      });
    },
    // 搜索按钮操作
    handleQuery(isExport) {
      this.queryParams.page = 1;
      if (isExport) {
        this.exportExcel();
      } else {
        this.getList();
      }
    },
    // 导出excel
    exportExcel() {
      // 使用axios进行导出，确保正确传递认证头部
      const url = process.env.VUE_APP_BASE_API + 'backendApi/member/exportUser';
      const params = { ...this.queryParams };

      // 构建查询参数
      const queryString = Object.keys(params)
        .filter(key => params[key] !== undefined && params[key] !== '')
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');

      const fullUrl = queryString ? `${url}?${queryString}` : url;

      // 使用axios下载
      axios({
        method: 'get',
        url: fullUrl,
        responseType: 'blob',
        headers: {
          'Access-Token': getToken()
        }
      }).then(response => {
        // 创建下载链接
        const blob = new Blob([response.data]);
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = `挂失会员数据_${new Date().getTime()}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);

        this.$modal.msgSuccess("导出成功");
      }).catch(error => {
        console.error('导出失败:', error);
        this.$modal.msgError("导出失败");
      });
    },
  }
};
</script>
<style scoped>
.custom-label >>> .el-form-item__label {
  width: 90px !important;
}
</style>

