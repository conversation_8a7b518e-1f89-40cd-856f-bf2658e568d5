import request from '@/utils/request'

// 分页查询积分规则列表
export function getPointRuleList(query) {
  return request({
    url: 'backendApi/pointRule/list',
    method: 'get',
    params: query
  })
}
// 查询积分规则
export function getPointRuleInfo(id) {
  return request({
    url: 'backendApi/pointRule/detail/' + id,
    method: 'get'
  })
}

// 分页查询积分明细列表
export function getPointList(query) {
  return request({
      url: 'backendApi/point/list',
      method: 'get',
      params: query
  })
}
// 查询商户信息
export function getAllMerchants(data) {
  return request({
    url: 'backendApi/merchant/searchMerchant',
    method: 'get',
    params: data
  })
}
// 查询会员等级信息
export function getAllMemberGrades() {
  return request({
    url: 'backendApi/userGrade/list',
    method: 'get'
  })
}

// 查询明细详情
export function getPointInfo(id) {
  return request({
    url: 'backendApi/userGrade/info/' + id,
    method: 'get'
  })
}

// 更新状态
export function updatePointStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
      url: 'backendApi/point/updateStatus',
      method: 'post',
      data: data
  })
}


// 删除积分规则信息
export function deletePointRule(id) {
  return request({
    url: 'backendApi/pointRule/delete' ,
    method: 'post',
    data: {
      id
    }
  })
}

// 获取配置信息
export function getSettingInfo() {
  return request({
    url: 'backendApi/point/setting',
    method: 'get'
  })
}

// 保存积分规则
export function saveSetting(data) {
  return request({
    url: 'backendApi/pointRule/save',
    method: 'post',
    data: data
  })
}
// 修改积分规则
export function upDateSetting(data) {
  return request({
    url: 'backendApi/pointRule/update',
    method: 'post',
    data: data
  })
}

// 确定充值
export function doRecharge(data) {
  return request({
    url: 'backendApi/point/doRecharge',
    method: 'post',
    data: data
  })
}

